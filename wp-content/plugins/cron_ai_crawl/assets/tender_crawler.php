<?php
declare(strict_types=1);

use GuzzleHttp\Client;
use GuzzleHttp\Psr7\HttpFactory;
use SoftCreatR\MistralAI\MistralAI;

require_once __DIR__ . '/vendor/autoload.php';

class TenderCrawler {
    private MistralAI $mistralAI;
    private const API_KEY = 'your_mistral_api_key'; // Replace with your actual API key
    private array $countries = [
        'DE' => 'Germany',
//        'FR' => 'France',
//        'UK' => 'United Kingdom',
//        'ES' => 'Spain',
//        'IT' => 'Italy',
        // Add more European countries as needed
    ];

    public function __construct() {
        $psr17Factory = new HttpFactory();
        $httpClient = new Client(['stream' => true]);

        $this->mistralAI = new MistralAI(
            requestFactory: $psr17Factory,
            streamFactory: $psr17Factory,
            uriFactory: $psr17Factory,
            httpClient: $httpClient,
            apiKey: self::API_KEY
        );
    }

    public function searchTenders(): array {
        $results = [];

        foreach ($this->countries as $code => $country) {
            $prompt = $this->createPrompt($country);
            $response = $this->queryMistralAI($prompt);

            if ($response) {
                $results[$code] = $this->parseResponse($response);
            }

            // Respect API rate limits
            sleep(1);
        }

        return $results;
    }

    private function createPrompt(string $country): string {
        return "Find current facility management tenders and procurement opportunities in {$country}. " .
               "Focus on: \n" .
               "1. Building maintenance services\n" .
               "2. Cleaning services\n" .
               "3. Security services\n" .
               "4. Technical facility management\n" .
               "5. Integrated facility management\n\n" .
               "Format the response as a structured list with: tender title, issuing organization, deadline, and estimated value if available.";
    }

    private function queryMistralAI(string $prompt): ?array {
        try {
            $parameters = [
                'model' => 'mistral-medium', // Or another appropriate model
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 1000
            ];

            $response = $this->mistralAI->createChatCompletion([], $parameters);

            if ($response) {
                return json_decode($response->getBody()->getContents(), true);
            }
        } catch (Exception $e) {
            error_log("Error querying Mistral.ai API: " . $e->getMessage());
        }

        return null;
    }

    private function parseResponse(array $response): array {
        $tenders = [];

        if (isset($response['choices'][0]['message']['content'])) {
            $content = $response['choices'][0]['message']['content'];

            // Basic parsing of the response - you might need to adjust this based on the actual response format
            $lines = explode("\n", $content);
            $currentTender = [];

            foreach ($lines as $line) {
                if (preg_match('/Title:\s*(.+)/', $line, $matches)) {
                    if (!empty($currentTender)) {
                        $tenders[] = $currentTender;
                    }
                    $currentTender = ['title' => $matches[1]];
                } elseif (preg_match('/Organization:\s*(.+)/', $line, $matches)) {
                    $currentTender['organization'] = $matches[1];
                } elseif (preg_match('/Deadline:\s*(.+)/', $line, $matches)) {
                    $currentTender['deadline'] = $matches[1];
                } elseif (preg_match('/Value:\s*(.+)/', $line, $matches)) {
                    $currentTender['value'] = $matches[1];
                }
            }

            if (!empty($currentTender)) {
                $tenders[] = $currentTender;
            }
        }

        return $tenders;
    }

    public function saveTenders(array $tenders, string $outputFile = 'facility_management_tenders.json'): void {
        $jsonData = json_encode($tenders, JSON_PRETTY_PRINT);
        file_put_contents($outputFile, $jsonData);
    }
}

// Usage example
try {
    $crawler = new TenderCrawler();
    $tenders = $crawler->searchTenders();
    $crawler->saveTenders($tenders);

    echo "Tender search completed. Results saved to facility_management_tenders.json\n";

    // Display summary
    foreach ($tenders as $countryCode => $countryTenders) {
        echo "\nTenders found in {$countryCode}: " . count($countryTenders) . "\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
```
This script provides the following features:
1. A class that manages the interaction with the Mistral.ai API `TenderCrawler`
2. Searches for facility management tenders across multiple European countries
3. Structured prompts focusing on specific facility management services
4. Response parsing and storage in JSON format
5. Rate limiting to respect API constraints
6. Error handling and logging

To use this script:
1. Install the required dependencies:
``` bash
composer require softcreatr/php-mistral-ai-sdk guzzlehttp/guzzle
```
1. Replace `'your_mistral_api_key'` with your actual Mistral.ai API key
2. Adjust the `$countries` array to include the European countries you're interested in
3. Run the script:
``` bash
php tender_crawler.php
```
The script will:
- Query the Mistral.ai API for each country
- Parse and structure the responses
- Save the results to a JSON file
- Display a summary of found tenders

Note: You might need to adjust the parsing logic in the `parseResponse` method depending on the actual format of the responses you receive from the Mistral.ai API.
Also consider adding:
- Database storage for the results
- More sophisticated error handling
- Pagination for large result sets
- Email notifications for new tenders
- Regular scheduling via cron jobs

Remember to respect the API's rate limits and terms of service when using this script.


## Me
php tender_crawler.php
Could not open input file: tender_crawler.php

## AI Assistant
Let's create the file first. Since you already have parts of the necessary infrastructure in your project, we'll create a new file called `tender_crawler.php` in your project directory.
``` php
<?php
declare(strict_types=1);

/*
 * Copyright (c) 2024
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 */

use GuzzleHttp\Client;
use GuzzleHttp\Psr7\HttpFactory;
use SoftCreatR\MistralAI\MistralAI;

// Ensure we have the correct path to the autoloader
$autoloadPaths = [
    __DIR__ . '/../assets/vendor/autoload.php',
    __DIR__ . '/../vendor/autoload.php',
    __DIR__ . '/vendor/autoload.php',
    dirname(__DIR__, 4) . '/vendor/autoload.php',
];

$autoloaded = false;
foreach ($autoloadPaths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $autoloaded = true;
        break;
    }
}

if (!$autoloaded) {
    die('Could not find autoloader. Please check your installation.');
}

class TenderCrawler {
    private MistralAI $mistralAI;
    private const API_KEY = 'bJeL4i8pJCsHAMq4fUpO4CxmEf0e1if5'; // Using the key from your demo_crawl.php
    private array $countries = [
        'DE' => 'Germany',
        'FR' => 'France',
        'GB' => 'United Kingdom',
        'ES' => 'Spain',
        'IT' => 'Italy'
    ];

    private array $sectors = [
        'Building maintenance',
        'Cleaning services',
        'Security services',
        'Technical facility management',
        'Integrated facility management'
    ];

    public function __construct() {
        $psr17Factory = new HttpFactory();
        $httpClient = new Client([
            'stream' => true,
        ]);

        $this->mistralAI = new MistralAI(
            requestFactory: $psr17Factory,
            streamFactory: $psr17Factory,
            uriFactory: $psr17Factory,
            httpClient: $httpClient,
            apiKey: self::API_KEY
        );
    }

    public function searchTenders(): array {
        $results = [];

        foreach ($this->countries as $code => $country) {
            echo "Searching tenders in $country...\n";

            foreach ($this->sectors as $sector) {
                $prompt = $this->createPrompt($country, $sector);
                $response = $this->queryMistralAI($prompt);

                if ($response) {
                    if (!isset($results[$code])) {
                        $results[$code] = [];
                    }
                    $results[$code][$sector] = $this->parseResponse($response);
                }

                // Respect API rate limits
                sleep(2);
            }
        }

        return $results;
    }

    private function createPrompt(string $country, string $sector): string {
        return "Find current tenders and procurement opportunities in {$country} for {$sector} in the facility management industry. " .
               "Include only active tenders. " .
               "Focus on contract value, submission deadline, and contracting authority. " .
               "Format as structured data with tender title, organization, deadline, and value.";
    }

    private function queryMistralAI(string $prompt): ?array {
        try {
            $parameters = [
                'model' => 'mistral-tiny',  // Using the model from your demo_crawl.php
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 2000
            ];

            // Using your existing MistralAIFactory for consistency
            $result = MistralAIFactory::request("createChatCompletion", $parameters);
            return $result;

        } catch (Exception $e) {
            error_log("Error querying Mistral.ai API: " . $e->getMessage());
            echo "Error: " . $e->getMessage() . "\n";
            return null;
        }
    }

    private function parseResponse(array $response): array {
        $tenders = [];

        if (isset($response['choices'][0]['message']['content'])) {
            $content = $response['choices'][0]['message']['content'];

            // Store raw response for debugging
            $tenders['raw_response'] = $content;

            // Try to extract structured data
            $lines = explode("\n", $content);
            $currentTender = [];

            foreach ($lines as $line) {
                $line = trim($line);

                // Skip empty lines
                if (empty($line)) continue;

                // Try to detect new tender entries
                if (preg_match('/^(Title|Tender|Project):/i', $line)) {
                    if (!empty($currentTender)) {
                        $tenders['parsed_tenders'][] = $currentTender;
                    }
                    $currentTender = ['title' => trim(substr($line, strpos($line, ':') + 1))];
                } elseif (preg_match('/Organization:|Authority:|Client:/i', $line)) {
                    $currentTender['organization'] = trim(substr($line, strpos($line, ':') + 1));
                } elseif (preg_match('/Deadline:|Due Date:|Submission Date:/i', $line)) {
                    $currentTender['deadline'] = trim(substr($line, strpos($line, ':') + 1));
                } elseif (preg_match('/Value:|Budget:|Amount:|Cost:/i', $line)) {
                    $currentTender['value'] = trim(substr($line, strpos($line, ':') + 1));
                } else {
                    // Store unmatched lines for debugging
                    if (!isset($currentTender['additional_info'])) {
                        $currentTender['additional_info'] = [];
                    }
                    $currentTender['additional_info'][] = $line;
                }
            }

            // Don't forget the last tender
            if (!empty($currentTender)) {
                $tenders['parsed_tenders'][] = $currentTender;
            }
        }

        return $tenders;
    }

    public function saveTenders(array $tenders, string $outputFile = 'facility_management_tenders.json'): void {
        $timestamp = date('Y-m-d_H-i-s');
        $outputFile = "tender_results_{$timestamp}.json";

        $jsonData = json_encode($tenders, JSON_PRETTY_PRINT);
        file_put_contents($outputFile, $jsonData);

        echo "\nResults saved to $outputFile\n";
    }
}

// Execute the crawler
try {
    echo "Starting tender crawler...\n";
    $crawler = new TenderCrawler();
    $tenders = $crawler->searchTenders();
    $crawler->saveTenders($tenders);

    // Display summary
    echo "\nSearch completed. Summary:\n";
    foreach ($tenders as $countryCode => $countryTenders) {
        echo "\n$countryCode:\n";
        foreach ($countryTenders as $sector => $sectorTenders) {
            $count = isset($sectorTenders['parsed_tenders']) ? count($sectorTenders['parsed_tenders']) : 0;
            echo "  - $sector: $count tenders found\n";
        }
    }

} catch (Exception $e) {
    echo "Fatal error: " . $e->getMessage() . "\n";
    exit(1);
}

if (!$autoloaded) {
    die('Could not find autoloader. Please check your installation.');
}

class TenderCrawler {
    private MistralAI $mistralAI;
    private const API_KEY = 'bJeL4i8pJCsHAMq4fUpO4CxmEf0e1if5';
    private array $countries = [
        'DE' => 'Germany',
        'FR' => 'France',
        'GB' => 'United Kingdom',
        'ES' => 'Spain',
        'IT' => 'Italy'
    ];
    
    private array $sectors = [
        'Building maintenance',
        'Cleaning services',
        'Security services',
        'Technical facility management',
        'Integrated facility management'
    ];

    public function __construct() {
        $psr17Factory = new HttpFactory();
        $httpClient = new Client(['stream' => true]);

        // Modified constructor call without named parameters
        $this->mistralAI = new MistralAI(
            $psr17Factory,
            $psr17Factory,
            $psr17Factory,
            $httpClient,
            self::API_KEY
        );
    }

    public function searchTenders(): array {
        $results = [];
        
        foreach ($this->countries as $code => $country) {
            echo "Searching tenders in $country...\n";
            
            foreach ($this->sectors as $sector) {
                $prompt = $this->createPrompt($country, $sector);
                $response = $this->queryMistralAI($prompt);
                
                if ($response) {
                    if (!isset($results[$code])) {
                        $results[$code] = [];
                    }
                    $results[$code][$sector] = $this->parseResponse($response);
                }
                
                sleep(2); // Respect API rate limits
            }
        }

        return $results;
    }

    private function createPrompt(string $country, string $sector): string {
        return "Find current tenders and procurement opportunities in {$country} for {$sector} in the facility management industry. " .
               "Include only active tenders. " .
               "Focus on contract value, submission deadline, and contracting authority. " .
               "Format as structured data with tender title, organization, deadline, and value.";
    }

    private function queryMistralAI(string $prompt): ?array {
        try {
            $parameters = [
                'model' => 'mistral-tiny',
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 2000
            ];

            $result = MistralAIFactory::request("createChatCompletion", $parameters);
            return $result;

        } catch (Exception $e) {
            error_log("Error querying Mistral.ai API: " . $e->getMessage());
            echo "Error: " . $e->getMessage() . "\n";
            return null;
        }
    }

    private function parseResponse(array $response): array {
        $tenders = [];

        if (isset($response['choices'][0]['message']['content'])) {
            $content = $response['choices'][0]['message']['content'];
            $tenders['raw_response'] = $content;

            $lines = explode("\n", $content);
            $currentTender = [];
            
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line)) continue;
                
                if (preg_match('/^(Title|Tender|Project):/i', $line)) {
                    if (!empty($currentTender)) {
                        $tenders['parsed_tenders'][] = $currentTender;
                    }
                    $currentTender = ['title' => trim(substr($line, strpos($line, ':') + 1))];
                } elseif (preg_match('/Organization:|Authority:|Client:/i', $line)) {
                    $currentTender['organization'] = trim(substr($line, strpos($line, ':') + 1));
                } elseif (preg_match('/Deadline:|Due Date:|Submission Date:/i', $line)) {
                    $currentTender['deadline'] = trim(substr($line, strpos($line, ':') + 1));
                } elseif (preg_match('/Value:|Budget:|Amount:|Cost:/i', $line)) {
                    $currentTender['value'] = trim(substr($line, strpos($line, ':') + 1));
                } else {
                    if (!isset($currentTender['additional_info'])) {
                        $currentTender['additional_info'] = [];
                    }
                    $currentTender['additional_info'][] = $line;
                }
            }

            if (!empty($currentTender)) {
                $tenders['parsed_tenders'][] = $currentTender;
            }
        }

        return $tenders;
    }

    public function saveTenders(array $tenders): void {
        $timestamp = date('Y-m-d_H-i-s');
        $outputFile = "tender_results_{$timestamp}.json";
        
        $jsonData = json_encode($tenders, JSON_PRETTY_PRINT);
        file_put_contents($outputFile, $jsonData);
        
        echo "\nResults saved to $outputFile\n";
    }
}

// Execute the crawler
try {
    echo "Starting tender crawler...\n";
    $crawler = new TenderCrawler();
    $tenders = $crawler->searchTenders();
    $crawler->saveTenders($tenders);
    
    // Display summary
    echo "\nSearch completed. Summary:\n";
    foreach ($tenders as $countryCode => $countryTenders) {
        echo "\n$countryCode:\n";
        foreach ($countryTenders as $sector => $sectorTenders) {
            $count = isset($sectorTenders['parsed_tenders']) ? count($sectorTenders['parsed_tenders']) : 0;
            echo "  - $sector: $count tenders found\n";
        }
    }
    
} catch (Exception $e) {
    echo "Fatal error: " . $e->getMessage() . "\n";
    exit(1);
}


