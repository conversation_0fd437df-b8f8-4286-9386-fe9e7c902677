<?php
/**
 * Plugin Name:       Sodexo AI Crawler
 * Plugin URI:        https://sodexo.com/
 * Description:       AI-powered web crawler for analyzing partner websites using Mistral AI. Crawls URLs from partner database and provides business intelligence analysis.
 * Version:           1.0.0
 * Author:            Sodexo Development Team
 * Author URI:        https://sodexo.com/
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       sodexo-ai-crawler
 * Domain Path:       /languages
 * Requires at least: 5.0
 * Tested up to:      6.8.2
 * Requires PHP:      7.4
 * Network:           false
 *
 * @package           Sodexo_AI_Crawler
 * @since             1.0.0
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	wp_die( 'Direct access not allowed.' );
}

// Define plugin constants
define( 'SODEXO_AI_CRAWLER_VERSION', '1.0.0' );
define( 'SODEXO_AI_CRAWLER_PLUGIN_NAME', 'sodexo-ai-crawler' );
define( 'SODEXO_AI_CRAWLER_PATH', plugin_dir_path( __FILE__ ) );
define( 'SODEXO_AI_CRAWLER_URL', plugin_dir_url( __FILE__ ) );
define( 'SODEXO_AI_CRAWLER_ASSETS_PATH', SODEXO_AI_CRAWLER_PATH . 'assets/' );
define( 'SODEXO_AI_CRAWLER_ASSETS_URL', SODEXO_AI_CRAWLER_URL . 'assets/' );

// Create upload directories for crawler data
$upload_dir = wp_upload_dir();
$crawler_temp = $upload_dir['basedir'] . '/sodexo-crawler-temp/';
$crawler_logs = $upload_dir['basedir'] . '/sodexo-crawler-logs/';

define( 'SODEXO_AI_CRAWLER_TEMP', $crawler_temp );
define( 'SODEXO_AI_CRAWLER_LOGS', $crawler_logs );

/**
 * Main plugin class
 */
class Sodexo_AI_Crawler {

    /**
     * Initialize the plugin
     */
    public function __construct() {
        add_action( 'init', array( $this, 'init' ) );
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );

        // Include the Mistral AI cron functionality
        if ( file_exists( SODEXO_AI_CRAWLER_PATH . 'mistral-ai-cron.php' ) ) {
            require_once SODEXO_AI_CRAWLER_PATH . 'mistral-ai-cron.php';
        }
    }

    /**
     * Initialize plugin functionality
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain( 'sodexo-ai-crawler', false, dirname( plugin_basename( __FILE__ ) ) . '/languages/' );

        // Create necessary directories
        $this->create_directories();
    }

    /**
     * Create necessary directories
     */
    private function create_directories() {
        $dirs = array(
            SODEXO_AI_CRAWLER_TEMP,
            SODEXO_AI_CRAWLER_LOGS
        );

        foreach ( $dirs as $dir ) {
            if ( ! file_exists( $dir ) ) {
                wp_mkdir_p( $dir );
                // Add index.php for security
                file_put_contents( $dir . 'index.php', '<?php // Silence is golden' );
            }
        }
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_management_page(
            __( 'Sodexo AI Crawler', 'sodexo-ai-crawler' ),
            __( 'AI Crawler', 'sodexo-ai-crawler' ),
            'manage_options',
            'sodexo-ai-crawler',
            array( $this, 'admin_page' )
        );
    }

    /**
     * Admin page content
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1><?php _e( 'Sodexo AI Crawler', 'sodexo-ai-crawler' ); ?></h1>
            <p><?php _e( 'AI-powered web crawler for analyzing partner websites using Mistral AI.', 'sodexo-ai-crawler' ); ?></p>

            <div class="card">
                <h2><?php _e( 'Crawler Tools', 'sodexo-ai-crawler' ); ?></h2>
                <p><strong><?php _e( 'Partner Crawler:', 'sodexo-ai-crawler' ); ?></strong>
                   <?php printf( __( 'Run the crawler script: %s', 'sodexo-ai-crawler' ),
                         '<code>php ' . SODEXO_AI_CRAWLER_ASSETS_PATH . 'tender_crawler.php</code>' ); ?>
                </p>
                <p><strong><?php _e( 'Check Results:', 'sodexo-ai-crawler' ); ?></strong>
                   <?php printf( __( 'View results: %s', 'sodexo-ai-crawler' ),
                         '<code>php ' . SODEXO_AI_CRAWLER_ASSETS_PATH . 'detailed_results.php</code>' ); ?>
                </p>
            </div>

            <div class="card">
                <h2><?php _e( 'Database Tables', 'sodexo-ai-crawler' ); ?></h2>
                <p><?php _e( 'The plugin uses the following database tables:', 'sodexo-ai-crawler' ); ?></p>
                <ul>
                    <li><code>wp_sodexo_partners</code> - <?php _e( 'Partner information and URLs', 'sodexo-ai-crawler' ); ?></li>
                    <li><code>wp_crawl_results</code> - <?php _e( 'Crawling results and AI analysis', 'sodexo-ai-crawler' ); ?></li>
                    <li><code>wp_mistral_ai_logs</code> - <?php _e( 'Mistral AI API logs', 'sodexo-ai-crawler' ); ?></li>
                </ul>
            </div>
        </div>
        <?php
    }
}

/**
 * Plugin activation hook
 */
function activate_sodexo_ai_crawler() {
    // Create directories and set up initial data
    $plugin = new Sodexo_AI_Crawler();

    // Flush rewrite rules
    flush_rewrite_rules();
}

/**
 * Plugin deactivation hook
 */
function deactivate_sodexo_ai_crawler() {
    // Clean up scheduled events
    wp_clear_scheduled_hook( 'mistral_ai_cron_hook' );

    // Flush rewrite rules
    flush_rewrite_rules();
}

// Register activation and deactivation hooks
register_activation_hook( __FILE__, 'activate_sodexo_ai_crawler' );
register_deactivation_hook( __FILE__, 'deactivate_sodexo_ai_crawler' );

// Initialize the plugin
new Sodexo_AI_Crawler();
