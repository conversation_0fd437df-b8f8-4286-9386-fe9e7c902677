<?php
/**
 * Import Sodexo_Ingenieurbüros.csv into sodexo_partners table
 * This script creates the table if it doesn't exist and imports the CSV data
 */

// Database connection details
$host = 'localhost';
$username = 'wp_sodexo_juni25';
$password = 'ydGG8U5V_kjsd7NAc8F';
$database = 'wp_sodexo_juni25';

// Extract port from host if specified
$port = 3306;
if (strpos($host, ':') !== false) {
    list($host, $port) = explode(':', $host);
}

// Create database connection
$mysqli = new mysqli($host, $username, $password, $database, $port);

// Check connection
if ($mysqli->connect_error) {
    die("Connection failed: " . $mysqli->connect_error);
}

// Set charset to handle special characters
$mysqli->set_charset("utf8mb4");

echo "Connected to database successfully.\n";

// Table name with WordPress prefix
$table_name = 'wp_sodexo_partners';

// Check if table exists
$result = $mysqli->query("SHOW TABLES LIKE '$table_name'");
if ($result->num_rows == 0) {
    echo "Table $table_name does not exist. Creating it...\n";
    
    // Create table based on CSV structure: Name;email;ansprechpartner;url
    $create_table_sql = "CREATE TABLE $table_name (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        ansprechpartner VARCHAR(255),
        url VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($mysqli->query($create_table_sql)) {
        echo "Table $table_name created successfully.\n";
    } else {
        die("Error creating table: " . $mysqli->error);
    }
} else {
    echo "Table $table_name already exists.\n";
}

// Path to CSV file
$csv_file = 'wp-content/plugins/cron_ai_crawl/assets/Sodexo_Ingenieurbüros.csv';

if (!file_exists($csv_file)) {
    die("CSV file not found: $csv_file\n");
}

echo "Reading CSV file: $csv_file\n";

// Open CSV file
$handle = fopen($csv_file, 'r');
if (!$handle) {
    die("Could not open CSV file.\n");
}

// Read header line
$header = fgetcsv($handle, 1000, ';');
echo "CSV Header: " . implode(', ', $header) . "\n";

// Prepare insert statement
$insert_sql = "INSERT INTO $table_name (name, email, ansprechpartner, url) VALUES (?, ?, ?, ?) 
               ON DUPLICATE KEY UPDATE 
               email = VALUES(email), 
               ansprechpartner = VALUES(ansprechpartner), 
               url = VALUES(url),
               updated_at = CURRENT_TIMESTAMP";

$stmt = $mysqli->prepare($insert_sql);
if (!$stmt) {
    die("Prepare failed: " . $mysqli->error);
}

$imported_count = 0;
$skipped_count = 0;
$line_number = 1; // Start from 1 since we already read the header

// Process each line
while (($data = fgetcsv($handle, 1000, ';')) !== FALSE) {
    $line_number++;
    
    // Skip empty lines
    if (empty($data[0]) && empty($data[1]) && empty($data[2]) && empty($data[3])) {
        $skipped_count++;
        continue;
    }
    
    // Clean and prepare data
    $name = trim($data[0] ?? '');
    $email = trim($data[1] ?? '');
    $ansprechpartner = trim($data[2] ?? '');
    $url = trim($data[3] ?? '');
    
    // Skip if name is empty (required field)
    if (empty($name)) {
        echo "Skipping line $line_number: Empty name\n";
        $skipped_count++;
        continue;
    }
    
    // Clean URL - remove www. prefix if present and ensure it's clean
    if (!empty($url)) {
        $url = preg_replace('/^www\./', '', $url);
        $url = trim($url);
    }
    
    // Bind parameters and execute
    $stmt->bind_param("ssss", $name, $email, $ansprechpartner, $url);
    
    if ($stmt->execute()) {
        $imported_count++;
        echo "Imported: $name\n";
    } else {
        echo "Error importing line $line_number ($name): " . $stmt->error . "\n";
    }
}

// Close file and statement
fclose($handle);
$stmt->close();

echo "\n=== Import Summary ===\n";
echo "Total lines processed: " . ($line_number - 1) . "\n";
echo "Successfully imported: $imported_count\n";
echo "Skipped: $skipped_count\n";

// Display final count in table
$count_result = $mysqli->query("SELECT COUNT(*) as total FROM $table_name");
$count_row = $count_result->fetch_assoc();
echo "Total records in table: " . $count_row['total'] . "\n";

// Display sample of imported data
echo "\n=== Sample of imported data ===\n";
$sample_result = $mysqli->query("SELECT * FROM $table_name LIMIT 5");
while ($row = $sample_result->fetch_assoc()) {
    echo "ID: {$row['id']}, Name: {$row['name']}, Email: {$row['email']}, Contact: {$row['ansprechpartner']}, URL: {$row['url']}\n";
}

$mysqli->close();
echo "\nImport completed successfully!\n";
?>
