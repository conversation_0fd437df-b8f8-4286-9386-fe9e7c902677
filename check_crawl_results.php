<?php
/**
 * Check the current crawl results from wp_crawl_results table
 */

require_once 'class_sql_executor.php';

// Database connection
$db = new SQL_Executor(
    'localhost',
    'wp_sodexo_juni25', 
    'ydGG8U5V_kjsd7NAc8F',
    'wp_sodexo_juni25',
    3306
);

echo "=== Crawl Results Summary ===\n\n";

// Get summary statistics
$summaryQuery = "
    SELECT 
        crawl_status,
        COUNT(*) as count,
        AVG(response_time_ms) as avg_response_time
    FROM wp_crawl_results 
    GROUP BY crawl_status
";

$summary = $db->select($summaryQuery);
if ($summary) {
    echo "Status Summary:\n";
    foreach ($summary as $row) {
        echo "  {$row['crawl_status']}: {$row['count']} records (avg response: " . 
             number_format($row['avg_response_time'], 2) . "ms)\n";
    }
    echo "\n";
}

// Get recent results
$recentQuery = "
    SELECT 
        partner_name,
        partner_url,
        crawl_status,
        response_time_ms,
        LEFT(facility_services_found, 100) as services_preview,
        crawl_timestamp
    FROM wp_crawl_results 
    ORDER BY id DESC 
    LIMIT 10
";

$recent = $db->select($recentQuery);
if ($recent) {
    echo "Recent Results (last 10):\n";
    echo str_repeat("-", 80) . "\n";
    foreach ($recent as $row) {
        echo "Company: {$row['partner_name']}\n";
        echo "URL: {$row['partner_url']}\n";
        echo "Status: {$row['crawl_status']} ({$row['response_time_ms']}ms)\n";
        echo "Services: " . ($row['services_preview'] ?: 'N/A') . "\n";
        echo "Time: {$row['crawl_timestamp']}\n";
        echo str_repeat("-", 40) . "\n";
    }
}

// Get total count
$countQuery = "SELECT COUNT(*) as total FROM wp_crawl_results";
$count = $db->select($countQuery);
if ($count) {
    echo "\nTotal records in database: " . $count[0]['total'] . "\n";
}

echo "\nDone!\n";
?>
