<?php
/**
 * Verify the import of Sodexo partners data
 */

// Database connection details
$host = 'localhost';
$username = 'wp_sodexo_juni25';
$password = 'ydGG8U5V_kjsd7NAc8F';
$database = 'wp_sodexo_juni25';
$port = 3306;

// Create database connection
$mysqli = new mysqli($host, $username, $password, $database, $port);

// Check connection
if ($mysqli->connect_error) {
    die("Connection failed: " . $mysqli->connect_error);
}

// Set charset
$mysqli->set_charset("utf8mb4");

$table_name = 'wp_sodexo_partners';

echo "=== Verification Report for $table_name ===\n\n";

// Check table structure
echo "1. Table Structure:\n";
$structure_result = $mysqli->query("DESCRIBE $table_name");
while ($row = $structure_result->fetch_assoc()) {
    echo "   {$row['Field']} - {$row['Type']} - {$row['Null']} - {$row['Key']}\n";
}

// Count total records
echo "\n2. Total Records:\n";
$count_result = $mysqli->query("SELECT COUNT(*) as total FROM $table_name");
$count_row = $count_result->fetch_assoc();
echo "   Total records: " . $count_row['total'] . "\n";

// Show all records
echo "\n3. All Imported Records:\n";
$all_result = $mysqli->query("SELECT * FROM $table_name ORDER BY id");
while ($row = $all_result->fetch_assoc()) {
    echo "   ID: {$row['id']}\n";
    echo "   Name: {$row['name']}\n";
    echo "   Email: {$row['email']}\n";
    echo "   Contact Person: {$row['ansprechpartner']}\n";
    echo "   URL: {$row['url']}\n";
    echo "   Created: {$row['created_at']}\n";
    echo "   ---\n";
}

// Statistics
echo "\n4. Statistics:\n";
$stats_result = $mysqli->query("
    SELECT 
        COUNT(*) as total_records,
        COUNT(CASE WHEN email != '' AND email IS NOT NULL THEN 1 END) as records_with_email,
        COUNT(CASE WHEN ansprechpartner != '' AND ansprechpartner IS NOT NULL THEN 1 END) as records_with_contact,
        COUNT(CASE WHEN url != '' AND url IS NOT NULL THEN 1 END) as records_with_url
    FROM $table_name
");
$stats = $stats_result->fetch_assoc();
echo "   Total records: {$stats['total_records']}\n";
echo "   Records with email: {$stats['records_with_email']}\n";
echo "   Records with contact person: {$stats['records_with_contact']}\n";
echo "   Records with URL: {$stats['records_with_url']}\n";

$mysqli->close();
echo "\nVerification completed!\n";
?>
